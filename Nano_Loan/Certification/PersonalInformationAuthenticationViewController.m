//
//  PersonalInformationAuthenticationViewController.m
//  Nano_<PERSON><PERSON>
//
//  需求说明：
//  • 支持 editMode 参数：YES 时进入已完成信息编辑模式，可拉取服务器信息展示并允许修改；NO 时新认证流程。
//  • 通过 splendid 传入产品ID，所有接口需带此参数。
//  • 页面需拉取个人信息接口，展示填写项；未完成项高亮；完成项灰色可编辑。
//  • 保存成功后刷新接口，更新状态。
//

#import "PersonalInformationAuthenticationViewController.h"
#import "NetworkManager.h"
#import <MBProgressHUD/MBProgressHUD.h>
#import "AddressDataManager.h"
#import "AddressPickerViewController.h"
#import "CustomAddressPickerViewController.h"
#import "HUD.h"
#import "AddressNode.h"
#import "RiskEventManager.h"
#import "OptionPickerViewController.h"

#define SCALE_375 (UIScreen.mainScreen.bounds.size.width / 375.0)

@interface PersonalInformationAuthenticationViewController () <UITableViewDataSource, UITableViewDelegate, UITextFieldDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<NSDictionary *> *items; // 接口字段数组
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSString *> *values; // 用户填写内容 key=modest
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSString *> *displayValues; // 枚举/地址等用于 UI 展示的值
@property (nonatomic, assign) NSTimeInterval pageStartTime; // 埋点开始时间
@property (nonatomic, strong) UIView *navView;            // 自定义导航
@property (nonatomic, strong) UIImageView *panelView;     // 内容背景
@property (nonatomic, strong) UIButton *nextButton;       // 底部 Next 按钮
@property (nonatomic, strong) UIScrollView *scrollView;   // 表单滚动视图
@property (nonatomic, strong) NSLayoutConstraint *panelBottomConstraint; // 背景图底部约束
@property (nonatomic, strong) UIImageView *stepLogoBg;    // 步骤logo背景图
@property (nonatomic, strong) UIImageView *stepLogoBgView; // 步骤logo背景图

// 键盘处理相关属性
@property (nonatomic, assign) CGFloat keyboardHeight;
@property (nonatomic, assign) CGFloat originalScrollViewBottom;
@property (nonatomic, weak) UITextField *activeTextField;

@end

@implementation PersonalInformationAuthenticationViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // 通用背景
    [self setupBackgroundImage];
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    // 记录埋点开始时间
    self.pageStartTime = [[NSDate date] timeIntervalSince1970];

    // 自定义导航
    [self setupCustomNavBar];

    // 内容 UI（背景 + table + Next 按钮）
    [self buildUI];

    // 初始化用于展示的字典
    self.displayValues = [NSMutableDictionary dictionary];

    // 添加点击空白处收起键盘
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissKeyboard)];
    tap.cancelsTouchesInView = NO;
    [self.view addGestureRecognizer:tap];

    // 设置键盘通知
    [self setupKeyboardNotifications];

    // 拉取数据
    [self fetchPersonalInfo];
}

#pragma mark - UI

- (void)setupTableView {
    UITableViewStyle style = UITableViewStyleGrouped;
    if (@available(iOS 13.0, *)) {
        style = UITableViewStyleInsetGrouped;
    }
    self.tableView = [[UITableView alloc] initWithFrame:self.view.bounds style:style];
    self.tableView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    [self.view addSubview:self.tableView];

    // Footer with Save button
    UIView *footer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.view.bounds.size.width, 100)];
    UIButton *saveBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    saveBtn.frame = CGRectMake(20, 30, self.view.bounds.size.width - 40, 50);
    saveBtn.layer.cornerRadius = 25;
    [saveBtn setTitle:@"Save" forState:UIControlStateNormal];
    [saveBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    saveBtn.backgroundColor = UIColor.systemBlueColor;
    [saveBtn addTarget:self action:@selector(saveTapped) forControlEvents:UIControlEventTouchUpInside];
    [footer addSubview:saveBtn];
    self.tableView.tableFooterView = footer;
}

#pragma mark - Networking

- (void)fetchPersonalInfo {
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    hud.label.text = @"Loading…";
    NSDictionary *params = self.splendid ? @{ @"splendid": self.splendid } : @{};
    __weak typeof(self) weakSelf = self;
//    [NetworkManager requestWithAPI:@"Alicia/fearlessness" params:params method:@"GET" completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
    [NetworkManager postFormWithAPI:@"Alicia/fearlessness" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [hud hideAnimated:YES];
            if (error) {
                MBProgressHUD *err = [MBProgressHUD showHUDAddedTo:weakSelf.view animated:YES];
                err.mode = MBProgressHUDModeText;
                err.label.text = error.localizedDescription ?: @"Network error";
                [err hideAnimated:YES afterDelay:1.5];
                return;
            }
            NSDictionary *awkward = response[@"awkward"];
            NSArray *madeup = awkward[@"madeup"];
            if (![madeup isKindOfClass:[NSArray class]]) return;
            weakSelf.items = [madeup mutableCopy];
            weakSelf.values = [NSMutableDictionary dictionary];
            weakSelf.displayValues = [NSMutableDictionary dictionary];
            for (NSDictionary *item in madeup) {
                NSString *key = item[@"modest"] ?: @"";
                NSString *currentDisplay = item[@"eyebrows"] ?: @""; // 回显展示值
                NSString *was = item[@"wasbetty"] ?: @"";
                id subjectCodeObj = item[@"subject"]; // 可能为空

                BOOL isEnum = ([was isEqualToString:@"always"] && [item[@"takeanother"] count] > 0);
                if (isEnum) {
                    // 枚举类型：保存时需提交 subjectCode
                    if (subjectCodeObj) {
                        weakSelf.values[key] = [subjectCodeObj description];
                    }
                    if (currentDisplay.length > 0) {
                        weakSelf.displayValues[key] = currentDisplay;
                    }
                } else {
                    // 普通文本或地址等，直接用回显字符串
                    if (currentDisplay.length > 0) {
                        weakSelf.values[key] = currentDisplay;
                    }
                }
            }
            [weakSelf.tableView reloadData];
            // 数据加载完成后更新表格高度
            [weakSelf updateTableViewHeight];
        });
    }];
}

#pragma mark - Save

- (void)saveTapped {
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    hud.label.text = @"Saving…";

    // 构建上传参数：基于拉取接口返回的字段键值
    NSMutableDictionary *params = [self.values mutableCopy];
    if (self.splendid) params[@"splendid"] = self.splendid;

    __weak typeof(self) weakSelf = self;
    [NetworkManager postFormWithAPI:@"Alicia/eventhe" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [hud hideAnimated:YES];

            NSString *message = nil;
            if (response && [response isKindOfClass:[NSDictionary class]]) {
                message = response[@"patted"] ?: @"<nil>";
                NSLog(@"[Save Personal Info] patted: %@", message);
                
                // 若保存成功（modest == 0），记录埋点结束时间并上报
                NSNumber *modest = response[@"modest"];
                if ([modest respondsToSelector:@selector(integerValue)] && [modest integerValue] == 0) {
                    // 修正：应该上报埋点5（个人信息认证），不是埋点6
                    NSTimeInterval endTime = [[NSDate date] timeIntervalSince1970];
                    NSLog(@"👤 [埋点5-个人信息认证] 开始上报 startTime:%.0f endTime:%.0f", weakSelf.pageStartTime, endTime);
                    [RiskEventManager reportEventType:RiskEventTypePersonalInfo
                                          startTime:weakSelf.pageStartTime
                                            endTime:endTime
                                            orderId:nil];
                    
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [weakSelf.navigationController popViewControllerAnimated:YES];
                    });
                }
            } else if (error) {
                message = error.localizedDescription ?: @"Request error";
                NSLog(@"[Save Personal Info] Request error: %@", message);
            }

            // 弹出用户提示
            if (message.length > 0) {
                MBProgressHUD *toast = [MBProgressHUD showHUDAddedTo:weakSelf.view animated:YES];
                toast.mode = MBProgressHUDModeText;
                toast.label.text = message;
                [toast hideAnimated:YES afterDelay:1.5];
            }
        });
    }];
}

#pragma mark - UITableViewDataSource

// 每个 item 独立 section，仅一行
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.items.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *cid = @"FieldCell";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cid];
    if (!cell) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:cid];
        cell.backgroundColor = UIColor.clearColor;

        // 创建标题标签
        UILabel *titleLabel = [[UILabel alloc] init];
        titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
        titleLabel.font = [UIFont systemFontOfSize:14];
        titleLabel.textColor = [UIColor blackColor];
        titleLabel.tag = 100;
        [cell.contentView addSubview:titleLabel];

        // 背景图 - 放在标题下方
        UIImageView *bg = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"personallnformation_cell_bg"]];
        bg.translatesAutoresizingMaskIntoConstraints = NO;
        bg.tag = 102;
        [cell.contentView addSubview:bg];

        UITextField *tf = [[UITextField alloc] initWithFrame:CGRectZero];
        tf.translatesAutoresizingMaskIntoConstraints = NO;
        tf.textAlignment = NSTextAlignmentLeft;
        tf.tag = 101;
        tf.delegate = self;
        [cell.contentView addSubview:tf];

        // 约束布局：标题在上方，背景图和输入框在下方
        [NSLayoutConstraint activateConstraints:@[
            // 标题约束
            [titleLabel.leadingAnchor constraintEqualToAnchor:cell.contentView.leadingAnchor constant:16],
            [titleLabel.topAnchor constraintEqualToAnchor:cell.contentView.topAnchor constant:8],
            [titleLabel.trailingAnchor constraintLessThanOrEqualToAnchor:cell.contentView.trailingAnchor constant:-16],

            // 背景图约束 - 距离标题6pt
            [bg.leadingAnchor constraintEqualToAnchor:cell.contentView.leadingAnchor],
            [bg.trailingAnchor constraintEqualToAnchor:cell.contentView.trailingAnchor],
            [bg.topAnchor constraintEqualToAnchor:titleLabel.bottomAnchor constant:6],
            [bg.bottomAnchor constraintEqualToAnchor:cell.contentView.bottomAnchor constant:-5],

            // 输入框约束 - 在背景图内部，左对齐
            [tf.leadingAnchor constraintEqualToAnchor:bg.leadingAnchor constant:16],
            [tf.trailingAnchor constraintEqualToAnchor:bg.trailingAnchor constant:-16],
            [tf.centerYAnchor constraintEqualToAnchor:bg.centerYAnchor]
        ]];
    }
    NSDictionary *item = self.items[indexPath.section];

    // 设置标题
    UILabel *titleLabel = [cell.contentView viewWithTag:100];
    titleLabel.text = item[@"coldchicken"] ?: @"";

    UITextField *tf = [cell.contentView viewWithTag:101];
    tf.placeholder = item[@"replies"] ?: @"";

    NSString *key = item[@"modest"] ?: @"";
    tf.text = self.values[key];

    NSString *was = item[@"wasbetty"];
    NSArray *options = item[@"takeanother"];
    if (([was isEqualToString:@"always"] && options.count > 0) || [was isEqualToString:@"hearing"]) {
        // Picker类型，无文本框，可点行选择
        tf.hidden = YES;

        // 创建显示标签替代textField
        UILabel *displayLabel = [cell.contentView viewWithTag:103];
        if (!displayLabel) {
            displayLabel = [[UILabel alloc] init];
            displayLabel.translatesAutoresizingMaskIntoConstraints = NO;
            displayLabel.textAlignment = NSTextAlignmentLeft;
            displayLabel.tag = 103;
            [cell.contentView addSubview:displayLabel];

            UIImageView *bg = [cell.contentView viewWithTag:102];
            [NSLayoutConstraint activateConstraints:@[
                [displayLabel.leadingAnchor constraintEqualToAnchor:bg.leadingAnchor constant:16],
                [displayLabel.trailingAnchor constraintEqualToAnchor:bg.trailingAnchor constant:-60], // 为箭头留空间
                [displayLabel.centerYAnchor constraintEqualToAnchor:bg.centerYAnchor]
            ]];
        }
        displayLabel.hidden = NO;

        // 添加箭头图标
        UIImageView *arrowView = [cell.contentView viewWithTag:104];
        if (!arrowView) {
            arrowView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"setting_cell_arrow"]];
            arrowView.translatesAutoresizingMaskIntoConstraints = NO;
            arrowView.tag = 104;
            [cell.contentView addSubview:arrowView];

            UIImageView *bg = [cell.contentView viewWithTag:102];
            [NSLayoutConstraint activateConstraints:@[
                [arrowView.trailingAnchor constraintEqualToAnchor:bg.trailingAnchor constant:-20],
                [arrowView.centerYAnchor constraintEqualToAnchor:bg.centerYAnchor],
                [arrowView.widthAnchor constraintEqualToConstant:12],
                [arrowView.heightAnchor constraintEqualToConstant:12]
            ]];
        }
        arrowView.hidden = NO;

        NSString *display = self.displayValues[key] ?: self.values[key] ?: @"";
        BOOL isPlaceholder = NO;

        // 修复：如果没有有效的显示值，使用占位文本
        if (display.length == 0 || [display isEqualToString:@"0"]) {
            if ([was isEqualToString:@"hearing"]) {
                // 地址选择类型，始终显示英文占位
                display = @"Please select";
                isPlaceholder = YES;
            } else {
                // 枚举选择类型，只有后台下发了占位文字才显示
                NSString *placeholder = item[@"replies"];
                if (placeholder && placeholder.length > 0) {
                    display = placeholder;
                    isPlaceholder = YES;
                } else {
                    display = @"";
                    isPlaceholder = NO;
                }
            }
        }

        displayLabel.text = display;
        // 设置文字颜色：有实际内容时为黑色，占位文字时为灰色
        displayLabel.textColor = isPlaceholder ? [UIColor lightGrayColor] : [UIColor blackColor];
    } else {
        tf.hidden = NO;
        // 隐藏选择器相关视图
        UILabel *displayLabel = [cell.contentView viewWithTag:103];
        UIImageView *arrowView = [cell.contentView viewWithTag:104];
        displayLabel.hidden = YES;
        arrowView.hidden = YES;

        if ([item[@"particular"] integerValue] == 1) {
            tf.keyboardType = UIKeyboardTypeNumberPad;
        } else {
            tf.keyboardType = UIKeyboardTypeDefault;
        }
    }

    return cell;
}

#pragma mark - Cell 间距（Section Header 用空视图实现）

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    // 首行顶部不需要额外间距
    return section == 0 ? 0 : 5.0;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    UIView *gap = [[UIView alloc] init];
    gap.backgroundColor = UIColor.clearColor;
    return gap;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return 0.01; // 消除 footer 默认高度
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    // 先收起键盘，避免键盘遮挡选择器
    [self.view endEditing:YES];
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSDictionary *item = self.items[indexPath.section];
    NSString *was = item[@"wasbetty"];
    if ([was isEqualToString:@"always"]) {
        NSArray *options = item[@"takeanother"];
        if (options.count == 0) return;
        NSMutableArray<NSString *> *titles = [NSMutableArray array];
        for (NSDictionary *opt in options) {
            NSString *t = opt[@"excitedbecause"] ?: @"";
            if (t.length) [titles addObject:t];
        }
        NSString *key = item[@"modest"];
        OptionPickerViewController *picker = [[OptionPickerViewController alloc] initWithTitle:item[@"coldchicken"]
                                                                                       options:titles
                                                                                selectedTitle:self.displayValues[key]
                                                                                     completion:^(NSInteger selectedIndex, NSString * _Nullable selectedTitle) {
            if (selectedTitle.length) {
                // 保存 subject code，回显 excitedbecause
                if (selectedIndex != NSNotFound && selectedIndex < options.count) {
                    id subj = options[selectedIndex][@"subject"];
                    if (subj) {
                        self.values[key] = [subj description];
                    }
                }
                self.displayValues[key] = selectedTitle;
                [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
            }
        }];
        [self presentViewController:picker animated:NO completion:nil];
    } else if ([was isEqualToString:@"hearing"]) {
        // 地址三级选择 - 使用自定义选择器替代系统选择器
        NSString *key = item[@"modest"];
        __weak typeof(self) weakSelf = self;
        [[AddressDataManager sharedManager] fetchAddressDataIfNeededWithCompletion:^(NSArray<AddressNode *> * _Nullable roots, NSError * _Nullable error) {
            if (roots.count > 0) {
                // 使用自定义地址选择器
                CustomAddressPickerViewController *picker = [[CustomAddressPickerViewController alloc] initWithAddressRoots:roots selection:^(AddressNode * _Nonnull province, AddressNode * _Nonnull city, AddressNode * _Nonnull district) {
                    NSString *addr = [NSString stringWithFormat:@"%@|%@|%@", province.name ?: @"", city.name ?: @"", district.name ?: @""];
                    weakSelf.values[key] = addr;
                    [weakSelf.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
                }];
                
                // 使用全屏模态样式呈现
                picker.modalPresentationStyle = UIModalPresentationOverFullScreen;
                picker.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
                [weakSelf presentViewController:picker animated:NO completion:nil];
            } else {
                [HUD showError:error ?: [NSError errorWithDomain:@"Address" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Address data not available"}] inView:weakSelf.view];
            }
        }];
    }
}

#pragma mark - UITextFieldDelegate

- (void)textFieldDidBeginEditing:(UITextField *)textField {
    self.activeTextField = textField;
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    self.activeTextField = nil;
    UITableViewCell *cell = (UITableViewCell *)textField.superview.superview;
    NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
    if (!indexPath) return;
    NSDictionary *item = self.items[indexPath.section];
    NSString *key = item[@"modest"] ?: @"";
    self.values[key] = textField.text ?: @"";
}

#pragma mark - Properties

@synthesize items = _items;
@synthesize values = _values;
@synthesize displayValues = _displayValues;
@synthesize tableView = _tableView;

#pragma mark - Private Methods

// 新增方法：设置背景图
- (void)setupBackgroundImage {
    UIImageView *bgView = [[UIImageView alloc] initWithFrame:self.view.bounds];
    bgView.image = [UIImage imageNamed:@"general_background"];
    bgView.contentMode = UIViewContentModeScaleAspectFill;
    bgView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    bgView.userInteractionEnabled = NO;
    bgView.alpha = 1.0; // 可根据需要调整透明度
    [self.view insertSubview:bgView atIndex:0];
}

- (void)setupCustomNavBar {
    self.navView = [[UIView alloc] init];
    self.navView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.navView];
    UILayoutGuide *safe = self.view.safeAreaLayoutGuide;
    [NSLayoutConstraint activateConstraints:@[
        [self.navView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor],
        [self.navView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor],
        [self.navView.topAnchor constraintEqualToAnchor:safe.topAnchor],
        [self.navView.heightAnchor constraintEqualToConstant:44]
    ]];

    UIButton *backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [backBtn setImage:[UIImage imageNamed:@"nav_back"] forState:UIControlStateNormal];
    backBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [backBtn addTarget:self action:@selector(backAction) forControlEvents:UIControlEventTouchUpInside];
    [self.navView addSubview:backBtn];
    [NSLayoutConstraint activateConstraints:@[
        [backBtn.leadingAnchor constraintEqualToAnchor:self.navView.leadingAnchor constant:16],
        [backBtn.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor],
        [backBtn.widthAnchor constraintEqualToConstant:34],
        [backBtn.heightAnchor constraintEqualToConstant:34]
    ]];

    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Identity Authentication"; // 与第一步页面保持一致
    titleLabel.font = [UIFont boldSystemFontOfSize:22];
    titleLabel.textColor = UIColor.whiteColor;
    titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.navView addSubview:titleLabel];
    [NSLayoutConstraint activateConstraints:@[
        [titleLabel.centerXAnchor constraintEqualToAnchor:self.navView.centerXAnchor],
        [titleLabel.centerYAnchor constraintEqualToAnchor:self.navView.centerYAnchor]
    ]];
}

- (void)backAction {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)buildUI {
    // 先移除旧视图（适配下拉刷新等场景）
    [self.panelView removeFromSuperview];
    [self.nextButton removeFromSuperview];
    [self.scrollView removeFromSuperview];
    [self.stepLogoBg removeFromSuperview];

    // 重置约束引用
    self.panelBottomConstraint = nil;

    // 4. Next 按钮 - 先创建，用于确定滚动区域
    UIButton *nextBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    nextBtn.translatesAutoresizingMaskIntoConstraints = NO;
    [nextBtn setBackgroundImage:[UIImage imageNamed:@"home_apply_bg"] forState:UIControlStateNormal];
    [nextBtn setTitle:@"Next" forState:UIControlStateNormal];
    [nextBtn setTitleColor:UIColor.whiteColor forState:UIControlStateNormal];
    nextBtn.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:22.0];
    nextBtn.layer.cornerRadius = 25;
    nextBtn.clipsToBounds = YES;
    [nextBtn addTarget:self action:@selector(saveTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:nextBtn];
    self.nextButton = nextBtn;

    [NSLayoutConstraint activateConstraints:@[
        [nextBtn.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:14],
        [nextBtn.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-14],
        // 调整到安全区域底部，避免被其他视图遮挡并保证交互
        [nextBtn.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-20],
        [nextBtn.heightAnchor constraintEqualToConstant:50]
    ]];

    // 1. 内容背景图 panel - 改为可拉伸高度
    UIImageView *panel = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"authentication_c_bg"]]; // 若找不到则为空占位
    panel.userInteractionEnabled = YES;
    panel.translatesAutoresizingMaskIntoConstraints = NO;
    panel.layer.cornerRadius = 16.0;
    panel.clipsToBounds = YES;
    // 设置图片拉伸模式，让背景图能够跟随内容高度拉伸
    panel.contentMode = UIViewContentModeScaleToFill;
    [self.view addSubview:panel];
    self.panelView = panel;

    // 设置基本约束，底部约束将动态调整
    [NSLayoutConstraint activateConstraints:@[
        [panel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:14],
        [panel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-14],
        [panel.topAnchor constraintEqualToAnchor:self.navView.bottomAnchor constant:20]
    ]];

    // 初始设置背景图底部约束到 Next 按钮上方 20pt（最大高度）
    self.panelBottomConstraint = [panel.bottomAnchor constraintEqualToAnchor:nextBtn.topAnchor constant:-20];
    self.panelBottomConstraint.active = YES;

    // 2. Step Logo 背景图（在背景图顶部）
    UIImageView *stepLogoBg = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"personal_info_step_header_bg"]];
    stepLogoBg.translatesAutoresizingMaskIntoConstraints = NO;
    stepLogoBg.contentMode = UIViewContentModeScaleToFill; // 跟随宽度拉伸
    stepLogoBg.userInteractionEnabled = YES;
    [panel addSubview:stepLogoBg];
    self.stepLogoBg = stepLogoBg;

    // Step Logo 背景图约束：宽度=背景图宽度，高度按比例自适应
    CGFloat stepHeaderRatio = 79.0 / 347.0; // 原始比例 79/347
    [NSLayoutConstraint activateConstraints:@[
        [stepLogoBg.leadingAnchor constraintEqualToAnchor:panel.leadingAnchor],
        [stepLogoBg.trailingAnchor constraintEqualToAnchor:panel.trailingAnchor],
        [stepLogoBg.topAnchor constraintEqualToAnchor:panel.topAnchor],
        [stepLogoBg.heightAnchor constraintEqualToAnchor:stepLogoBg.widthAnchor multiplier:stepHeaderRatio]
    ]];

    // 3. Step 标题区内容（数字 + 文案 + illustration）- 放在 stepLogoBg 上
    UILabel *stepNumLabel = [[UILabel alloc] init];
    stepNumLabel.text = @"2";
    stepNumLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:40];
    stepNumLabel.textColor = [UIColor colorWithRed:160/255.0 green:233/255.0 blue:234/255.0 alpha:1.0];
    stepNumLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [stepLogoBg addSubview:stepNumLabel];

    UILabel *stepTitleLabel = [[UILabel alloc] init];
    stepTitleLabel.text = @"Identification\nParticulars"; // 按设计分两行
    stepTitleLabel.numberOfLines = 2;
    stepTitleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:14];
    stepTitleLabel.textColor = UIColor.blackColor;
    stepTitleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [stepLogoBg addSubview:stepTitleLabel];

    UIImageView *headerIllustration = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"Frame 1171276775"]];
    headerIllustration.translatesAutoresizingMaskIntoConstraints = NO;
    headerIllustration.contentMode = UIViewContentModeScaleAspectFit;
    [stepLogoBg addSubview:headerIllustration];

    [NSLayoutConstraint activateConstraints:@[
        [stepNumLabel.leadingAnchor constraintEqualToAnchor:stepLogoBg.leadingAnchor constant:14],
        [stepNumLabel.centerYAnchor constraintEqualToAnchor:stepLogoBg.centerYAnchor],
        [stepTitleLabel.leadingAnchor constraintEqualToAnchor:stepNumLabel.trailingAnchor constant:8],
        [stepTitleLabel.centerYAnchor constraintEqualToAnchor:stepLogoBg.centerYAnchor],
        [headerIllustration.trailingAnchor constraintEqualToAnchor:stepLogoBg.trailingAnchor constant:-14],
        [headerIllustration.centerYAnchor constraintEqualToAnchor:stepLogoBg.centerYAnchor],
        [headerIllustration.widthAnchor constraintEqualToConstant:113*SCALE_375],
        [headerIllustration.heightAnchor constraintEqualToConstant:70*SCALE_375]
    ]];

    // 3. 创建滚动视图容器，用于包含表单内容
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.translatesAutoresizingMaskIntoConstraints = NO;
    scrollView.backgroundColor = UIColor.clearColor;
    scrollView.showsVerticalScrollIndicator = YES;
    scrollView.showsHorizontalScrollIndicator = NO;
    scrollView.alwaysBounceVertical = NO; // 内容不足时不允许弹性滚动
    [self.view addSubview:scrollView];
    self.scrollView = scrollView;

    // 滚动视图约束：在背景图内部，从步骤logo背景图下方开始到背景图底部
    [NSLayoutConstraint activateConstraints:@[
        [scrollView.leadingAnchor constraintEqualToAnchor:panel.leadingAnchor constant:12],
        [scrollView.trailingAnchor constraintEqualToAnchor:panel.trailingAnchor constant:-12],
        [scrollView.topAnchor constraintEqualToAnchor:self.stepLogoBg.bottomAnchor], // 从步骤logo背景图下方开始
        [scrollView.bottomAnchor constraintEqualToAnchor:panel.bottomAnchor constant:-20] // 背景图底部上方20pt
    ]];

    // 4. TableView（放在滚动视图中，不限制高度）
    UITableViewStyle style = UITableViewStylePlain;
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:style];
    self.tableView.backgroundColor = UIColor.clearColor;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.rowHeight = 70.0; // 修改为两行布局的高度
    self.tableView.scrollEnabled = NO; // 禁用 tableView 自身滚动，由外层 scrollView 控制

    // iOS 15 以后默认会给 section header 顶部额外 16pt 间距，手动去除
    if (@available(iOS 15.0, *)) {
        self.tableView.sectionHeaderTopPadding = 0.0;
    }
    self.tableView.translatesAutoresizingMaskIntoConstraints = NO;
    [scrollView addSubview:self.tableView];

    // tableView 在滚动视图中的约束
    [NSLayoutConstraint activateConstraints:@[
        [self.tableView.leadingAnchor constraintEqualToAnchor:scrollView.leadingAnchor constant:12],
        [self.tableView.trailingAnchor constraintEqualToAnchor:scrollView.trailingAnchor constant:-12],
        [self.tableView.topAnchor constraintEqualToAnchor:scrollView.topAnchor],
        [self.tableView.bottomAnchor constraintEqualToAnchor:scrollView.bottomAnchor],
        // 设置 tableView 宽度与滚动视图内容区域一致
        [self.tableView.widthAnchor constraintEqualToAnchor:scrollView.widthAnchor constant:-24] // 减去左右边距
    ]];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:NO animated:animated];
}

- (void)dealloc {
    [self removeKeyboardNotifications];
}

#pragma mark - Keyboard

- (void)setupKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
}

- (void)removeKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:UIKeyboardWillShowNotification
                                                  object:nil];

    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                    name:UIKeyboardWillHideNotification
                                                  object:nil];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    // 获取键盘尺寸
    CGRect keyboardRect = [notification.userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    self.keyboardHeight = keyboardRect.size.height;

    // 动画持续时间
    NSTimeInterval duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];

    // 记录原始位置
    if (self.originalScrollViewBottom == 0) {
        self.originalScrollViewBottom = CGRectGetMaxY(self.scrollView.frame);
    }

    // 计算需要上移的距离
    CGFloat visibleHeight = self.view.bounds.size.height - self.keyboardHeight;
    CGFloat textFieldBottom = 0;

    if (self.activeTextField) {
        // 获取输入框在屏幕中的位置
        CGRect textFieldFrame = [self.activeTextField convertRect:self.activeTextField.bounds toView:self.view];
        textFieldBottom = CGRectGetMaxY(textFieldFrame) + 20; // 加20pt缓冲
    }

    // 如果输入框被键盘遮挡，则调整scrollView位置
    if (textFieldBottom > visibleHeight) {
        CGFloat offsetY = textFieldBottom - visibleHeight;

        [UIView animateWithDuration:duration animations:^{
            self.scrollView.contentOffset = CGPointMake(0, offsetY);
        }];
    }
}

- (void)keyboardWillHide:(NSNotification *)notification {
    // 恢复scrollView原始位置
    NSTimeInterval duration = [notification.userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];

    [UIView animateWithDuration:duration animations:^{
        self.scrollView.contentOffset = CGPointZero;
    }];

    self.keyboardHeight = 0;
}

- (void)dismissKeyboard {
    [self.view endEditing:YES];
}

#pragma mark - Dynamic Height Update

- (void)updateTableViewHeight {
    // 强制 tableView 重新计算内容大小
    [self.tableView layoutIfNeeded];

    // 计算 tableView 实际需要的高度
    CGFloat totalHeight = 0;
    for (NSInteger section = 0; section < [self.tableView numberOfSections]; section++) {
        // 计算每个 section 的高度
        totalHeight += [self tableView:self.tableView heightForHeaderInSection:section];
        totalHeight += [self tableView:self.tableView heightForFooterInSection:section];

        for (NSInteger row = 0; row < [self.tableView numberOfRowsInSection:section]; row++) {
            totalHeight += 70.0; // 使用新的行高
        }
    }

    // 更新 tableView 的高度约束
    // 移除之前的高度约束（如果存在）
    for (NSLayoutConstraint *constraint in self.tableView.constraints) {
        if (constraint.firstAttribute == NSLayoutAttributeHeight && constraint.firstItem == self.tableView) {
            constraint.active = NO;
        }
    }

    // 添加新的高度约束
    NSLayoutConstraint *heightConstraint = [self.tableView.heightAnchor constraintEqualToConstant:totalHeight];
    heightConstraint.active = YES;

    // 计算背景图需要的高度
    [self updatePanelHeight:totalHeight];

    // 更新布局
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.view layoutIfNeeded];
    });
}

- (void)updatePanelHeight:(CGFloat)tableViewHeight {
    // 计算步骤logo背景图的动态高度
    CGFloat panelWidth = self.view.bounds.size.width - 28; // 减去左右边距14*2
    CGFloat stepHeaderRatio = 79.0 / 347.0;
    CGFloat headerHeight = panelWidth * stepHeaderRatio; // 动态计算标题区高度

    CGFloat bottomPadding = 20; // 表单底部到背景图底部的间距
    CGFloat requiredPanelHeight = headerHeight + tableViewHeight + bottomPadding;

    // 计算背景图的最大允许高度（到Next按钮上方20pt）
    CGFloat maxPanelHeight = self.nextButton.frame.origin.y - (self.navView.frame.origin.y + self.navView.frame.size.height + 20) - 20;

    // 如果还没有布局完成，使用约束计算最大高度
    if (maxPanelHeight <= 0) {
        // 使用视图高度和安全区域计算
        CGFloat safeAreaBottom = self.view.safeAreaInsets.bottom;
        CGFloat nextButtonHeight = 50;
        CGFloat nextButtonBottomMargin = 20;
        CGFloat panelTopMargin = 20;
        CGFloat navHeight = 44;

        maxPanelHeight = self.view.bounds.size.height - safeAreaBottom - nextButtonHeight - nextButtonBottomMargin - panelTopMargin - navHeight - self.view.safeAreaInsets.top - 20;
    }

    // 更新背景图的底部约束
    self.panelBottomConstraint.active = NO;

    if (requiredPanelHeight <= maxPanelHeight) {
        // 表单内容较少，背景图高度刚好包含内容
        self.panelBottomConstraint = [self.panelView.heightAnchor constraintEqualToConstant:requiredPanelHeight];
        // 禁用滚动，因为内容都能显示
        self.scrollView.scrollEnabled = NO;
    } else {
        // 表单内容较多，背景图使用最大高度，启用滚动
        self.panelBottomConstraint = [self.panelView.bottomAnchor constraintEqualToAnchor:self.nextButton.topAnchor constant:-20];
        self.scrollView.scrollEnabled = YES;
    }

    self.panelBottomConstraint.active = YES;
}

@end

/*
保存用户信息（第二项）
请求方式 POST
请求地址 "/Alicia/eventhe":
场景 保存用户信息（第二项）
 "其他参数们，说明不止一个":    string    否    请查看上方获取用户信息（第二项）接口中返回的参数值是什么，有备注的
 "splendid":    string    否    产品id
 
 返回结果
 {
 "modest": "0",
 "patted": "success",
 "awkward": {}
 }

*/


/*
 {
   "modest": "0",
   "patted": "success",
   "awkward": {
       "farasked": "2023-08-25 07:24:44",
       "madeup": [
           {
               "darn": 19,
               "coldchicken": "Educational",//【重要】标题
               "replies": "Educational Qualification",//【重要】占位内容 -- 当 "eyebrows": 值为空时，用当前值占位显示
               "modest": "name",  //【重要】保存时提交的参数key
               "wasbetty": "enum",  //【重要】表单类型，参考接口文档左侧 7.值映射-认证项组件（对应混淆后的值）
               "particular": 0,//【重要】文本表单唤起的键盘类型：0系统默认的英文键盘，1系统纯数字键盘
               "eyebrows": "", //【重要】保存后的回显值
               "subject": 3,//【重要 - 枚举类型】保存后的key
               "takeanother": [//【重要】所有涉及枚举下拉说明：选者后表单回显取 "excitedbecause": ，保存时提交的值取 "subject":
                 {
                   "excitedbecause": "Primary",
                   "subject": 1
                 },
                 {
                   "excitedbecause": "middle school",
                   "subject": 2
                 },
                 {
                   "excitedbecause": "high school",
                   "subject": 3
                 }
               ],
               "thisto": 0,
               "amused": 0,
               "listening": "未认证",
               "poeplewere": true,
               "whose": 0
           },
           {
               "darn": 20,
               "coldchicken": "Company Address",
               "replies": "Company Address",
               "modest": "drifts",
               "wasbetty": "ago3",
               "takeanother": [],
               "thisto": 0,
               "amused": 0,
               "listening": "未认证",
               "poeplewere": true,
               "eyebrows": "",
               "whose": 0
           },
           {
               "darn": 21,
               "coldchicken": "Company Phone",
               "replies": "Company Phone",
               "modest": "barnyards",
               "wasbetty": "ago2",
               "takeanother": [],
               "thisto": 0,
               "amused": 0,
               "listening": "未认证",
               "poeplewere": true,
               "eyebrows": "",
               "whose": 0
           },
           {
               "darn": 68,
               "coldchicken": "Monthly Income",
               "replies": "Monthly Income",
               "modest": "catnip",
               "wasbetty": "ago1",
               "takeanother": [
                   {
                       "excitedbecause": "10000<",
                       "subject": 1
                   },
                   {
                       "excitedbecause": "10000-30000",
                       "subject": 2
                   },
                   {
                       "excitedbecause": "30000-50000",
                       "subject": 3
                   },
                   {
                       "excitedbecause": "50000-100000",
                       "subject": 4
                   },
                   {
                       "excitedbecause": "100000-200000",
                       "subject": 5
                   },
                   {
                       "excitedbecause": ">200000",
                       "subject": 6
                   }
               ],
               "thisto": 0,
               "amused": 0,
               "listening": "未认证",
               "poeplewere": true,
               "eyebrows": "",
               "whose": 0
           },
             
           {
               "darn": 73,
               "coldchicken": "Salary Type",
               "replies": "Salary Type",
               "modest": "slippery",
               "wasbetty": "ago1",
               "takeanother": [
                   {
                       "excitedbecause": "weekly",
                       "subject": 1
                   },
                   {
                       "excitedbecause": "biweekly",
                       "subject": 2
                   },
                   {
                       "excitedbecause": "monthly",
                       "subject": 3
                   },
                   {
                       "excitedbecause": "per day",
                       "subject": 4
                   },
                   {
                       "excitedbecause": "other",
                       "subject": 5
                   }
               ],
               "thisto": 0,
               "amused": 0,
               "listening": "未认证",
               "poeplewere": true,
               "eyebrows": "",
               "whose": 0
           }
       ]
   }
 }
 
 */
