#import "LocationManager.h"
#import "NetworkManager.h"
#import "AppDelegate.h"

static NSString * const kCachedLocationParamsKey = @"CachedLocationParams";
static NSString * const kLastUploadTimestampKey = @"LastLocationUploadDate";

@interface LocationManager ()
@property (nonatomic, strong) CLLocationManager *clManager;
@property (nonatomic, strong) CLGeocoder *geocoder;
@property (nonatomic, strong) CLLocation *lastLocation;
@end

@implementation LocationManager

+ (instancetype)sharedManager {
    static LocationManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[LocationManager alloc] initPrivate];
    });
    return instance;
}

// 私有 init，防止直接实例化
- (instancetype)initPrivate {
    self = [super init];
    if (self) {
        _clManager = [[CLLocationManager alloc] init];
        _clManager.delegate = self;
        _clManager.desiredAccuracy = kCLLocationAccuracyHundredMeters;
        _clManager.distanceFilter = 500; // 500m 更新一次即可
        if (@available(iOS 11.0, *)) {
            _clManager.showsBackgroundLocationIndicator = NO;
        }
        _geocoder = [[CLGeocoder alloc] init];
    }
    return self;
}

#pragma mark - Public Methods

- (void)startUpdatingLocation {
    // 如果尚未授权，仅申请 When-In-Use 权限，避免出现“始终允许”选项
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    if (status == kCLAuthorizationStatusNotDetermined) {
        // iOS 13+ 使用统一的权限请求 API
        if (@available(iOS 13.0, *)) {
            [self.clManager requestWhenInUseAuthorization];
        } else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
            [self.clManager requestWhenInUseAuthorization];
#pragma clang diagnostic pop
        }
    } else if (status == kCLAuthorizationStatusAuthorizedWhenInUse || status == kCLAuthorizationStatusAuthorizedAlways) {
        // 如果已经授权，直接开始定位
        NSLog(@"[LocationManager] 权限已授权，直接开始定位");
    }
    // 开始定位，系统会在授权通过后回调
    [self.clManager startUpdatingLocation];
}

- (nullable NSDictionary *)cachedReportParams {
    return [[NSUserDefaults standardUserDefaults] objectForKey:kCachedLocationParamsKey];
}

- (void)reportLocationIfCached {
    if (!self.lastLocation) {
        NSLog(@"[LocationManager] 暂无可用定位数据，等待定位回调");
        return;
    }
    // 反地理编码并上报
    [self.geocoder reverseGeocodeLocation:self.lastLocation completionHandler:^(NSArray<CLPlacemark *> * _Nullable placemarks, NSError * _Nullable error) {
        if (error || placemarks.count == 0) { return; }
        CLPlacemark *placemark = placemarks.firstObject;
        NSMutableDictionary *params = [NSMutableDictionary dictionary];
        params[@"darrein"] = placemark.administrativeArea ?: @"";
        params[@"italways"] = placemark.ISOcountryCode ?: @"";
        params[@"unkindly"] = placemark.country ?: @"";
        // 使用街道信息（thoroughfare）而非店铺名称，若无街道信息则回退到 placemark.name
        params[@"askedme"] = placemark.thoroughfare ?: placemark.name ?: @"";
        params[@"invitation"] = [NSString stringWithFormat:@"%f", self.lastLocation.coordinate.latitude];
        params[@"hersuch"] = [NSString stringWithFormat:@"%f", self.lastLocation.coordinate.longitude];
        params[@"sharethe"] = placemark.locality ?: placemark.administrativeArea ?: @"";
        params[@"adoration"] = placemark.subLocality ?: placemark.subAdministrativeArea ?: @"";
        [self uploadLocationParams:params];
    }];
}

- (CLLocation *)latestLocation {
    return self.lastLocation;
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations {
    CLLocation *location = locations.lastObject;
    if (!location) { return; }
    self.lastLocation = location;
    // 不再缓存参数，直接等待需要时生成
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    NSLog(@"[LocationManager] 定位失败: %@", error);
}

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    NSLog(@"[LocationManager] 定位权限状态变化: %d", (int)status);

    switch (status) {
        case kCLAuthorizationStatusAuthorizedWhenInUse:
        case kCLAuthorizationStatusAuthorizedAlways:
            NSLog(@"[LocationManager] 定位权限已授权，开始定位");
            [self.clManager startUpdatingLocation];
            // 发送通知告知首页权限已开启
            [[NSNotificationCenter defaultCenter] postNotificationName:@"LocationPermissionGranted" object:nil];
            break;
        case kCLAuthorizationStatusDenied:
        case kCLAuthorizationStatusRestricted:
            NSLog(@"[LocationManager] 定位权限被拒绝或受限");
            [self.clManager stopUpdatingLocation];
            break;
        case kCLAuthorizationStatusNotDetermined:
            NSLog(@"[LocationManager] 定位权限未确定");
            break;
        default:
            break;
    }
}

#pragma mark - Upload

- (void)uploadLocationParams:(NSDictionary *)params {
    [NetworkManager postFormWithAPI:@"Alicia/cardboardcontainers" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
        if (error) {
            NSLog(@"[LocationManager] 位置上报失败: %@", error);
        } else {
            NSLog(@"[LocationManager] 位置上报成功: %@", response);
        }
    }];
}

@end 
